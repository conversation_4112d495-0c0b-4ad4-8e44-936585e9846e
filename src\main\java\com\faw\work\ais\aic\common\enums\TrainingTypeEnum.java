package com.faw.work.ais.aic.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;


/**
 * 知识类型枚举
 *
 * <AUTHOR>
 * @date 2025/06/29
 */
@Getter
@AllArgsConstructor
public enum TrainingTypeEnum {
    /**
     * 商务政策
     */
    BUSINESS_POLICY("1", "policy"),

    /**
     * 产品手册
     */
    PRODUCT_MANUAL("2", "产品手册"),

    /**
     * 培训课件：汽车知识问题
     */
    TRAINING_COURSEWARE("3", "car_knowledge"),

    /**
     * 技术辅导
     */
    TECHNICAL_GUIDANCE("4", "技术辅导"),

    /**
     * 管理制度
     */
    MANAGEMENT_SYSTEM("5", "common"),

    /**
     * 通知
     */
    NOTICE("6", "通知"),

    /**
     * 营销创新知识
     */
    MARKETING_INNOVATION_KNOWLEDGE("7", "营销创新知识"),

    /**
     * 技术支援
     */
    TECHNICAL_SUPPORT("8", "技术支援"),

    /**
     * 竞品话术
     */
    COMPETITOR_SPEECH("9", "compare"),

    /**
     * 产品视频
     */
    PRODUCT_VIDEO("10", "产品视频");

    private final String code;
    private final String desc;

    public static String getByCode(String code) {
        for (TrainingTypeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value.getDesc();
            }
        }
        return null;
    }
}