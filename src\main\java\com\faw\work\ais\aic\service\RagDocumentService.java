package com.faw.work.ais.aic.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.faw.work.ais.aic.model.domain.RagDocumentPO;
import com.faw.work.ais.aic.model.request.*;
import com.faw.work.ais.aic.model.response.RagDocumentProcessAllResponse;
import com.faw.work.ais.aic.model.response.SimilarContentSearchResponse;
import jakarta.servlet.http.HttpServletResponse;

import java.util.List;

/**
 * 文档表 服务接口
 *
 * <AUTHOR> Assistant
 */
public interface RagDocumentService extends IService<RagDocumentPO> {

    /**
     * 根据条件查询文档列表
     *
     * @param request 查询条件
     * @return 文档列表
     */
    List<RagDocumentPO> getDocumentList(RagDocumentPO request);

    /**
     * 分页查询文档
     *
     * @param request 分页查询请求
     * @return 分页结果
     */
    Page<RagDocumentPO> getDocumentPage(RagDocumentPageRequest request);

    /**
     * 自定义分页查询文档
     *
     * @param page 分页参数
     * @param queryWrapper 查询条件
     * @return 分页结果
     */
    Page<RagDocumentPO> pageWithCustom(Page<RagDocumentPO> page, Wrapper<RagDocumentPO> queryWrapper);

    /**
     * 解析并切分文档
     *
     * @param documentId 文档ID
     */
    void splitDocument(Long documentId);

    /**
     * 为文档片段生成向量并存储到向量数据库
     *
     * @param documentId 文档ID
     * @return 是否成功
     */
    boolean vectorDocumentSplits(Long documentId);


    /**
     * 搜索相似内容并返回新的响应格式
     *
     * @param request 搜索请求
     * @return 相似内容搜索响应列表
     */
    List<SimilarContentSearchResponse> searchSimilarContentNew(SearchContentRequest request);



    /**
     * 处理文件上传并新增文档
     *
     * @param request 新增文档请求（包含文件）
     * @return 新增的文档对象
     */
    RagDocumentPO upload(RagDocumentAddRequest request);

    /**
     * 删除文档的向量索引
     *
     * @param documentId 文档ID
     * @return 是否成功
     */
    Long deleteDocumentVectors(Long documentId);


    /**
     * 删除文档切片信息
     * 只能删除未向量化或向量化失败的文档的切片信息
     *
     * @param documentId 文档ID
     * @return 是否成功
     */
    boolean deleteDocumentSplits(Long documentId);

    /**
     * 下载文档
     *
     * @param documentId 文档标识
     * @param response   响应
     */
    void download(Long documentId, HttpServletResponse response);

    /**
     * 通过id更新
     *
     * @param document 文件
     */
    void updateByIdTran(RagDocumentPO document);



    /**
     * 基于URL的文档一体化处理（保存文档+分片+绑定知识库+向量化）
     *
     * @param request 基于URL的一体化处理请求
     * @return 处理结果
     */
    RagDocumentProcessAllResponse processDocumentByUrl(RagDocumentProcessByUrlRequest request);

    /**
     * 同步知识中心文档（获取文档列表+过滤新文档+批量处理）
     *
     * @param request 同步知识中心文档请求
     * @return 同步处理结果
     */
    String syncKnowledgeDocuments(SyncKnowledgeDocumentsRequest request);

    /**
     * 处理同步的文档（解析、切分、绑定、向量化）
     *
     * @param request 处理同步文档请求
     * @return 处理结果
     */
    String processSyncedDocuments(ProcessSyncedDocumentsRequest request);
}
